/**
 * Example of standardized tool registration using ToolFactory
 * This demonstrates the improved security, error handling, and consistency patterns
 */

import { z } from "zod";
import * as fs from "fs/promises";
import * as path from "path";
import { XcodeServer } from "../../server.js";
import {
  ToolFactory,
  CommonSchemas,
  ToolCategories,
  ToolResult,
} from "../../utils/toolFactory.js";
import {
  PathAccessError,
  FileOperationError,
  ProjectNotFoundError,
} from "../../utils/errors.js";
import { SecureMessageFormatter } from "../../utils/common.js";
import { getProjectInfo } from "../../utils/project.js";

/**
 * Example: Standardized set_projects_base_dir tool
 */
export function registerSetProjectsBaseDirTool(server: XcodeServer) {
  ToolFactory.registerTool(
    server,
    "set_projects_base_dir_v2",
    "Sets the base directory where your Xcode projects are stored (standardized version).",
    z.object({
      baseDir: CommonSchemas.filePath.describe(
        "Path to the directory containing your Xcode projects. Supports ~ for home directory and environment variables."
      ),
    }),
    async (params, server) => {
      // Use our PathManager to expand and validate the path
      const expandedPath = server.pathManager.expandPath(params.baseDir);
      const stats = await fs.stat(expandedPath);

      if (!stats.isDirectory()) {
        throw new Error("Provided baseDir is not a directory");
      }

      // Update both the server config and PathManager
      server.config.projectsBaseDir = expandedPath;
      server.pathManager.setProjectsBaseDir(expandedPath);

      // Try to detect active project (don't fail if this fails)
      try {
        await server.detectActiveProject();
      } catch (error) {
        console.warn(
          SecureMessageFormatter.formatError(
            "Failed to auto-detect project after setting base directory",
            { error: error instanceof Error ? error.message : String(error) }
          )
        );
      }

      return ToolFactory.createSuccessResult(
        `Projects base directory set to: ${SecureMessageFormatter.formatPath(
          expandedPath
        )}`
      );
    },
    {
      requiresProject: false,
      validatePaths: true,
      timeout: 10000,
      cache: {
        enabled: false, // Don't cache directory changes
      },
    },
    {
      category: ToolCategories.PROJECT,
      description: "Sets the base directory for project discovery",
      examples: [
        "set_projects_base_dir_v2 ~/Documents/XcodeProjects",
        "set_projects_base_dir_v2 /Users/<USER>/Projects",
      ],
      securityLevel: "medium",
      requiresValidation: true,
    }
  );
}

/**
 * Example: Standardized get_active_project tool
 */
export function registerGetActiveProjectTool(server: XcodeServer) {
  ToolFactory.registerTool(
    server,
    "get_active_project_v2",
    "Retrieves detailed information about the currently active Xcode project (standardized version).",
    z.object({
      detailed: CommonSchemas.optionalBoolean.describe(
        "If true, include additional detailed project information"
      ),
    }),
    async (params, server) => {
      if (!server.activeProject) {
        await server.detectActiveProject();
      }

      if (!server.activeProject) {
        throw new ProjectNotFoundError("No active Xcode project detected");
      }

      // Get basic project info with caching
      const cacheKey = `project_info_${server.activeProject.path}`;
      const projectInfo = await server.cache.getOrSet(
        cacheKey,
        async () => {
          const { getProjectInfo } = await import("../../utils/project.js");
          return await getProjectInfo(
            server.activeProject!.path,
            server.commandExecutor
          );
        },
        300000 // 5 minutes TTL
      );

      // Include current active directory
      const activeDirectory = server.directoryState.getActiveDirectory();

      let result: any = {
        ...server.activeProject,
        ...projectInfo,
        activeDirectory: SecureMessageFormatter.formatPath(activeDirectory),
      };

      // Add detailed information if requested
      if (params.detailed) {
        try {
          // Get project configuration manually since the function doesn't exist
          const projectInfo = await getProjectInfo(
            server.activeProject.path,
            server.commandExecutor
          );
          const config = {
            configurations: projectInfo.configurations,
            schemes: projectInfo.schemes,
            targets: projectInfo.targets,
            defaultConfiguration: projectInfo.configurations[0] || "Debug",
          };

          result.configurations = config.configurations;
          result.schemes = config.schemes;
          result.targets = config.targets;
          result.defaultConfiguration = config.defaultConfiguration;
        } catch (error) {
          console.warn(
            SecureMessageFormatter.formatError(
              "Failed to get detailed project configuration",
              { error: error instanceof Error ? error.message : String(error) }
            )
          );
          result.detailedInfoError = "Failed to retrieve detailed information";
        }
      }

      return ToolFactory.createSuccessResult(
        "Active Project Information",
        result
      );
    },
    {
      requiresProject: false, // We handle project detection internally
      validatePaths: false,
      timeout: 15000,
      cache: {
        enabled: true,
        ttl: 60000, // 1 minute for project info
        keyGenerator: (params) =>
          `active_project_${params.detailed ? "detailed" : "basic"}`,
      },
    },
    {
      category: ToolCategories.PROJECT,
      description: "Retrieves information about the active Xcode project",
      examples: [
        "get_active_project_v2",
        "get_active_project_v2 --detailed true",
      ],
      seeAlso: ["set_project_path", "find_projects"],
      securityLevel: "low",
    }
  );
}

/**
 * Example: Standardized change_directory tool with enhanced security
 */
export function registerChangeDirectoryTool(server: XcodeServer) {
  ToolFactory.registerTool(
    server,
    "change_directory_v2",
    "Changes the active directory for relative path operations (standardized version).",
    z.object({
      directoryPath: CommonSchemas.filePath.describe(
        "Path to the directory to set as active. Supports absolute paths, paths relative to the current active directory, and ~ for home directory."
      ),
    }),
    async (params, server) => {
      // Expand tilde first, then resolve the path
      const expandedPath = server.pathManager.expandPath(params.directoryPath);
      const resolvedPath = server.directoryState.resolvePath(expandedPath);

      // Validate the path is within allowed boundaries
      server.pathManager.validatePathForReading(resolvedPath);

      // Validate the directory exists
      const stats = await fs.stat(resolvedPath);
      if (!stats.isDirectory()) {
        throw new FileOperationError(
          "validate",
          resolvedPath,
          new Error("Path is not a directory")
        );
      }

      // Record the directory change for history
      const currentDir = server.directoryState.getActiveDirectory();
      server.pathManager.recordDirectoryChange(currentDir, resolvedPath);

      // Set as active directory
      server.directoryState.setActiveDirectory(resolvedPath);

      return ToolFactory.createSuccessResult(
        `Active directory changed to: ${SecureMessageFormatter.formatPath(
          resolvedPath
        )}`
      );
    },
    {
      requiresProject: false,
      validatePaths: true,
      timeout: 5000,
    },
    {
      category: ToolCategories.PROJECT,
      description: "Changes the active working directory",
      examples: [
        "change_directory_v2 ~/Documents",
        "change_directory_v2 ../src",
      ],
      seeAlso: ["push_directory", "pop_directory", "get_current_directory"],
      securityLevel: "medium",
      requiresValidation: true,
    }
  );
}

/**
 * Register all standardized project tools
 */
export function registerStandardizedProjectTools(server: XcodeServer) {
  registerSetProjectsBaseDirTool(server);
  registerGetActiveProjectTool(server);
  registerChangeDirectoryTool(server);
}
