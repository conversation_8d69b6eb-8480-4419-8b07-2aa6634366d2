import * as path from "path";
import * as os from "os";

/**
 * String manipulation utilities
 */
export class StringUtils {
  /**
   * Escape special characters in a string for use in a regular expression
   */
  static escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  }

  /**
   * Truncate a string to a maximum length with ellipsis
   */
  static truncate(str: string, maxLength: number, suffix = "..."): string {
    if (str.length <= maxLength) return str;
    return str.substring(0, maxLength - suffix.length) + suffix;
  }

  /**
   * Convert a string to camelCase
   */
  static toCamelCase(str: string): string {
    return str.replace(/[-_\s]+(.)?/g, (_, char) =>
      char ? char.toUpperCase() : ""
    );
  }

  /**
   * Convert a string to kebab-case
   */
  static toKebabCase(str: string): string {
    return str
      .replace(/([a-z])([A-Z])/g, "$1-$2")
      .replace(/[\s_]+/g, "-")
      .toLowerCase();
  }

  /**
   * Convert a string to snake_case
   */
  static toSnakeCase(str: string): string {
    return str
      .replace(/([a-z])([A-Z])/g, "$1_$2")
      .replace(/[\s-]+/g, "_")
      .toLowerCase();
  }

  /**
   * Check if a string is empty or contains only whitespace
   */
  static isBlank(str: string | null | undefined): boolean {
    return !str || str.trim().length === 0;
  }

  /**
   * Remove ANSI color codes from a string
   */
  static stripAnsiCodes(str: string): string {
    return str.replace(/\x1b\[[0-9;]*m/g, "");
  }

  /**
   * Parse a version string into components
   */
  static parseVersion(version: string): {
    major: number;
    minor: number;
    patch: number;
    prerelease?: string;
  } {
    const match = version.match(/^(\d+)\.(\d+)\.(\d+)(?:-(.+))?$/);
    if (!match) {
      throw new Error(`Invalid version format: ${version}`);
    }

    return {
      major: parseInt(match[1], 10),
      minor: parseInt(match[2], 10),
      patch: parseInt(match[3], 10),
      prerelease: match[4],
    };
  }

  /**
   * Compare two version strings
   */
  static compareVersions(a: string, b: string): number {
    const versionA = StringUtils.parseVersion(a);
    const versionB = StringUtils.parseVersion(b);

    if (versionA.major !== versionB.major)
      return versionA.major - versionB.major;
    if (versionA.minor !== versionB.minor)
      return versionA.minor - versionB.minor;
    if (versionA.patch !== versionB.patch)
      return versionA.patch - versionB.patch;

    // Handle prerelease versions
    if (versionA.prerelease && !versionB.prerelease) return -1;
    if (!versionA.prerelease && versionB.prerelease) return 1;
    if (versionA.prerelease && versionB.prerelease) {
      return versionA.prerelease.localeCompare(versionB.prerelease);
    }

    return 0;
  }
}

/**
 * Array manipulation utilities
 */
export class ArrayUtils {
  /**
   * Remove duplicates from an array
   */
  static unique<T>(array: T[]): T[] {
    return [...new Set(array)];
  }

  /**
   * Remove duplicates from an array using a key function
   */
  static uniqueBy<T, K>(array: T[], keyFn: (item: T) => K): T[] {
    const seen = new Set<K>();
    return array.filter((item) => {
      const key = keyFn(item);
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  /**
   * Group array elements by a key function
   */
  static groupBy<T, K extends string | number | symbol>(
    array: T[],
    keyFn: (item: T) => K
  ): Record<K, T[]> {
    return array.reduce((groups, item) => {
      const key = keyFn(item);
      if (!groups[key]) groups[key] = [];
      groups[key].push(item);
      return groups;
    }, {} as Record<K, T[]>);
  }

  /**
   * Chunk an array into smaller arrays of specified size
   */
  static chunk<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * Flatten a nested array
   */
  static flatten<T>(array: (T | T[])[]): T[] {
    return array.reduce<T[]>((flat, item) => {
      return flat.concat(Array.isArray(item) ? ArrayUtils.flatten(item) : item);
    }, []);
  }

  /**
   * Find the intersection of two arrays
   */
  static intersection<T>(a: T[], b: T[]): T[] {
    const setB = new Set(b);
    return a.filter((item) => setB.has(item));
  }

  /**
   * Find the difference between two arrays (items in a but not in b)
   */
  static difference<T>(a: T[], b: T[]): T[] {
    const setB = new Set(b);
    return a.filter((item) => !setB.has(item));
  }
}

/**
 * Object manipulation utilities
 */
export class ObjectUtils {
  /**
   * Deep clone an object
   */
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== "object") return obj;
    if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
    if (obj instanceof Array)
      return obj.map((item) => ObjectUtils.deepClone(item)) as unknown as T;
    if (typeof obj === "object") {
      const cloned = {} as T;
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          cloned[key] = ObjectUtils.deepClone(obj[key]);
        }
      }
      return cloned;
    }
    return obj;
  }

  /**
   * Deep merge two objects
   */
  static deepMerge<T extends Record<string, any>>(
    target: T,
    source: Partial<T>
  ): T {
    const result = { ...target };

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        const sourceValue = source[key];
        const targetValue = result[key];

        if (
          ObjectUtils.isObject(sourceValue) &&
          ObjectUtils.isObject(targetValue)
        ) {
          result[key] = ObjectUtils.deepMerge(targetValue, sourceValue as any);
        } else {
          result[key] = sourceValue as T[Extract<keyof T, string>];
        }
      }
    }

    return result;
  }

  /**
   * Check if a value is a plain object
   */
  static isObject(value: any): value is Record<string, any> {
    return value !== null && typeof value === "object" && !Array.isArray(value);
  }

  /**
   * Get a nested property value using dot notation
   */
  static get(obj: any, path: string, defaultValue?: any): any {
    const keys = path.split(".");
    let current = obj;

    for (const key of keys) {
      if (current === null || current === undefined || !(key in current)) {
        return defaultValue;
      }
      current = current[key];
    }

    return current;
  }

  /**
   * Set a nested property value using dot notation
   */
  static set(obj: any, path: string, value: any): void {
    const keys = path.split(".");
    let current = obj;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current) || !ObjectUtils.isObject(current[key])) {
        current[key] = {};
      }
      current = current[key];
    }

    current[keys[keys.length - 1]] = value;
  }

  /**
   * Pick specific properties from an object
   */
  static pick<T extends Record<string, any>, K extends keyof T>(
    obj: T,
    keys: K[]
  ): Pick<T, K> {
    const result = {} as Pick<T, K>;
    for (const key of keys) {
      if (key in obj) {
        result[key] = obj[key];
      }
    }
    return result;
  }

  /**
   * Omit specific properties from an object
   */
  static omit<T extends Record<string, any>, K extends keyof T>(
    obj: T,
    keys: K[]
  ): Omit<T, K> {
    const result = { ...obj };
    for (const key of keys) {
      delete result[key];
    }
    return result;
  }
}

/**
 * Path manipulation utilities
 */
export class PathUtils {
  /**
   * Normalize a path and resolve environment variables
   */
  static expandPath(inputPath: string): string {
    if (!inputPath) return inputPath;

    // Handle tilde expansion
    if (inputPath.startsWith("~")) {
      inputPath = path.join(os.homedir(), inputPath.slice(1));
    }

    // Handle environment variables
    inputPath = inputPath.replace(/\$([A-Za-z_][A-Za-z0-9_]*)/g, (_, name) => {
      return process.env[name] || "";
    });

    // Handle ${VAR} style variables
    inputPath = inputPath.replace(
      /\${([A-Za-z_][A-Za-z0-9_]*)}/g,
      (_, name) => {
        return process.env[name] || "";
      }
    );

    return path.resolve(inputPath);
  }

  /**
   * Get the relative path from one directory to another
   */
  static getRelativePath(from: string, to: string): string {
    return path.relative(from, to);
  }

  /**
   * Check if a path is a subdirectory of another path
   */
  static isSubdirectory(parent: string, child: string): boolean {
    const relative = path.relative(parent, child);
    return !relative.startsWith("..") && !path.isAbsolute(relative);
  }

  /**
   * Get the common ancestor path of multiple paths
   */
  static getCommonPath(paths: string[]): string {
    if (paths.length === 0) return "";
    if (paths.length === 1) return path.dirname(paths[0]);

    const normalizedPaths = paths.map((p) => path.resolve(p));
    const parts = normalizedPaths[0].split(path.sep);

    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      if (!normalizedPaths.every((p) => p.split(path.sep)[i] === part)) {
        return parts.slice(0, i).join(path.sep) || path.sep;
      }
    }

    return normalizedPaths[0];
  }
}

/**
 * Time and date utilities
 */
export class TimeUtils {
  /**
   * Format a duration in milliseconds to a human-readable string
   */
  static formatDuration(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
    return `${(ms / 3600000).toFixed(1)}h`;
  }

  /**
   * Create a debounced function
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null;

    return (...args: Parameters<T>) => {
      if (timeout) clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  }

  /**
   * Create a throttled function
   */
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle = false;

    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  }

  /**
   * Sleep for a specified number of milliseconds
   */
  static sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

/**
 * Validation utilities
 */
export class ValidationUtils {
  /**
   * Check if a string is a valid email address
   */
  static isEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Check if a string is a valid URL
   */
  static isUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check if a string is a valid UUID
   */
  static isUuid(uuid: string): boolean {
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Check if a value is a valid port number
   */
  static isPort(port: number): boolean {
    return Number.isInteger(port) && port >= 1 && port <= 65535;
  }

  /**
   * Check if a string contains only alphanumeric characters
   */
  static isAlphanumeric(str: string): boolean {
    return /^[a-zA-Z0-9]+$/.test(str);
  }
}
