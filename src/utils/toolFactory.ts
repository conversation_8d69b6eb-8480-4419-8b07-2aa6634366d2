import { z } from "zod";
import { XcodeServer } from "../server.js";
import { XcodeServerError, ValidationError } from "./errors.js";

/**
 * Tool handler function type
 */
export type ToolHandler<T = any> = (
  params: T,
  server: XcodeServer
) => Promise<ToolResult>;

/**
 * Tool result structure
 */
export interface ToolResult {
  content: Array<
    | {
        type: "text";
        text: string;
      }
    | {
        type: "image";
        data: string;
        mimeType: string;
      }
    | {
        type: "resource";
        resource: {
          uri: string;
          text?: string;
          mimeType?: string;
        };
      }
  >;
  isError?: boolean;
  _meta?: Record<string, unknown>;
}

/**
 * Tool registration options
 */
export interface ToolOptions {
  requiresProject?: boolean;
  validatePaths?: boolean;
  timeout?: number;
  retries?: number;
  cache?: {
    enabled: boolean;
    ttl?: number;
    keyGenerator?: (params: any) => string;
  };
}

/**
 * Tool metadata for documentation and validation
 */
export interface ToolMetadata {
  category: string;
  description: string;
  examples?: string[];
  seeAlso?: string[];
  version?: string;
  deprecated?: boolean;
  deprecationMessage?: string;
}

/**
 * Factory for creating standardized tool registrations
 */
export class ToolFactory {
  /**
   * Register a tool with standardized error handling and validation
   */
  static registerTool<T>(
    server: XcodeServer,
    name: string,
    description: string,
    schema: z.ZodSchema<T>,
    handler: ToolHandler<T>,
    options: ToolOptions = {},
    metadata?: ToolMetadata
  ): void {
    // For now, register tools directly without the wrapper to avoid type issues
    // The wrapper functionality can be added later once we resolve the MCP SDK type compatibility
    server.server.tool(
      name,
      description,
      schema as any,
      async (params: any, extra: any) => {
        try {
          const result = await handler(params, server);
          return result as any; // Type assertion to work around MCP SDK type strictness
        } catch (error) {
          return {
            content: [
              {
                type: "text",
                text: `Error: ${
                  error instanceof Error ? error.message : String(error)
                }`,
              },
            ],
            isError: true,
          } as any;
        }
      }
    );
  }

  /**
   * Register a project-dependent tool
   */
  static registerProjectTool<T>(
    server: XcodeServer,
    name: string,
    description: string,
    schema: z.ZodSchema<T>,
    handler: ToolHandler<T>,
    options: Omit<ToolOptions, "requiresProject"> = {},
    metadata?: ToolMetadata
  ): void {
    ToolFactory.registerTool(
      server,
      name,
      description,
      schema,
      handler,
      { ...options, requiresProject: true },
      metadata
    );
  }

  /**
   * Register a file operation tool
   */
  static registerFileTool<T>(
    server: XcodeServer,
    name: string,
    description: string,
    schema: z.ZodSchema<T>,
    handler: ToolHandler<T>,
    options: Omit<ToolOptions, "validatePaths"> = {},
    metadata?: ToolMetadata
  ): void {
    ToolFactory.registerTool(
      server,
      name,
      description,
      schema,
      handler,
      { ...options, validatePaths: true },
      metadata
    );
  }

  /**
   * Register a cached tool
   */
  static registerCachedTool<T>(
    server: XcodeServer,
    name: string,
    description: string,
    schema: z.ZodSchema<T>,
    handler: ToolHandler<T>,
    cacheOptions: NonNullable<ToolOptions["cache"]>,
    options: Omit<ToolOptions, "cache"> = {},
    metadata?: ToolMetadata
  ): void {
    ToolFactory.registerTool(
      server,
      name,
      description,
      schema,
      handler,
      { ...options, cache: { ...cacheOptions, enabled: true } },
      metadata
    );
  }

  /**
   * Wrap a tool handler with standardized functionality
   */
  private static wrapHandler<T>(
    handler: ToolHandler<T>,
    options: ToolOptions,
    metadata?: ToolMetadata
  ): (extra: any) => Promise<ToolResult> {
    return async (extra: any): Promise<ToolResult> => {
      const params = extra as T;
      const startTime = Date.now();

      try {
        // Check if tool is deprecated
        if (metadata?.deprecated) {
          console.warn(
            `Tool is deprecated: ${
              metadata.deprecationMessage || "No replacement specified"
            }`
          );
        }

        // Validate parameters
        ToolFactory.validateParameters(params);

        // Check project requirement
        if (options.requiresProject) {
          // This would be injected via the server instance
          // For now, we'll handle this in the individual tools
        }

        // Handle caching
        if (options.cache?.enabled) {
          const cacheKey = options.cache.keyGenerator
            ? options.cache.keyGenerator(params)
            : ToolFactory.generateCacheKey(params);

          // Try to get from cache first
          // Cache implementation would go here
        }

        // Execute the handler with timeout and retries
        const result = await ToolFactory.executeWithRetries(
          () => handler(params, {} as XcodeServer), // Server would be injected properly
          options.retries ?? 0,
          options.timeout
        );

        // Log execution time for performance monitoring
        const duration = Date.now() - startTime;
        if (duration > 5000) {
          // Log slow operations
          console.warn(`Slow tool execution: ${duration}ms`);
        }

        return result;
      } catch (error) {
        // Standardized error handling
        return ToolFactory.handleError(error, metadata);
      }
    };
  }

  /**
   * Validate tool parameters
   */
  private static validateParameters(params: any): void {
    if (params === null || params === undefined) {
      throw new ValidationError(
        "params",
        params,
        "Parameters cannot be null or undefined"
      );
    }

    // Additional validation logic can be added here
  }

  /**
   * Execute a function with retries and timeout
   */
  private static async executeWithRetries<T>(
    fn: () => Promise<T>,
    retries: number,
    timeout?: number
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        if (timeout) {
          return await ToolFactory.withTimeout(fn(), timeout);
        } else {
          return await fn();
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt < retries) {
          // Exponential backoff
          const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError || new Error("Operation failed after retries");
  }

  /**
   * Add timeout to a promise
   */
  private static withTimeout<T>(
    promise: Promise<T>,
    timeoutMs: number
  ): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) => {
        setTimeout(
          () => reject(new Error(`Operation timed out after ${timeoutMs}ms`)),
          timeoutMs
        );
      }),
    ]);
  }

  /**
   * Generate a cache key from parameters
   */
  private static generateCacheKey(params: any): string {
    try {
      return JSON.stringify(params, Object.keys(params).sort());
    } catch {
      return String(params);
    }
  }

  /**
   * Handle errors in a standardized way
   */
  private static handleError(
    error: unknown,
    metadata?: ToolMetadata
  ): ToolResult {
    let errorMessage: string;
    let errorType: string;

    if (error instanceof XcodeServerError) {
      errorMessage = error.getFormattedMessage();
      errorType = error.name;
    } else if (error instanceof Error) {
      errorMessage = error.message;
      errorType = error.name;
    } else {
      errorMessage = String(error);
      errorType = "UnknownError";
    }

    // Add context from metadata if available
    if (metadata) {
      errorMessage += `\n\nTool: ${metadata.category}`;
      if (metadata.seeAlso && metadata.seeAlso.length > 0) {
        errorMessage += `\nSee also: ${metadata.seeAlso.join(", ")}`;
      }
    }

    return {
      content: [
        {
          type: "text",
          text: `Error (${errorType}): ${errorMessage}`,
        },
      ],
    };
  }

  /**
   * Create a success result
   */
  static createSuccessResult(message: string, data?: any): ToolResult {
    const content: ToolResult["content"] = [
      {
        type: "text",
        text: message,
      },
    ];

    if (data) {
      content.push({
        type: "text",
        text: typeof data === "string" ? data : JSON.stringify(data, null, 2),
      });
    }

    return { content };
  }

  /**
   * Create an error result
   */
  static createErrorResult(error: string | Error): ToolResult {
    const message = error instanceof Error ? error.message : error;
    return {
      content: [
        {
          type: "text",
          text: `Error: ${message}`,
        },
      ],
    };
  }

  /**
   * Create a result with formatted output
   */
  static createFormattedResult(
    title: string,
    data: any,
    format: "json" | "yaml" | "text" = "json"
  ): ToolResult {
    let formattedData: string;

    switch (format) {
      case "json":
        formattedData = JSON.stringify(data, null, 2);
        break;
      case "text":
        formattedData = String(data);
        break;
      default:
        formattedData = JSON.stringify(data, null, 2);
    }

    return {
      content: [
        {
          type: "text",
          text: `${title}\n\n${formattedData}`,
        },
      ],
    };
  }
}

/**
 * Common tool schemas for reuse
 */
export const CommonSchemas = {
  filePath: z
    .string()
    .describe(
      "Path to the file. Can be absolute, relative to active directory, or use ~ for home directory."
    ),
  directoryPath: z
    .string()
    .describe(
      "Path to the directory. Can be absolute, relative to active directory, or use ~ for home directory."
    ),
  projectPath: z
    .string()
    .describe("Path to the .xcodeproj or .xcworkspace file."),
  optional: {
    encoding: z
      .string()
      .optional()
      .describe(
        "Encoding to use (e.g., 'utf-8', 'latin1'). Default is 'utf-8'."
      ),
    createIfMissing: z
      .boolean()
      .optional()
      .describe("If true, creates the file/directory if it doesn't exist."),
    recursive: z
      .boolean()
      .optional()
      .describe("If true, perform operation recursively."),
    force: z
      .boolean()
      .optional()
      .describe(
        "If true, force the operation even if it might be destructive."
      ),
  },
};

/**
 * Tool categories for organization
 */
export const ToolCategories = {
  PROJECT: "Project Management",
  FILE: "File Operations",
  BUILD: "Build & Compilation",
  SIMULATOR: "iOS Simulator",
  COCOAPODS: "CocoaPods",
  SPM: "Swift Package Manager",
  XCODE: "Xcode Tools",
  UTILITY: "Utilities",
} as const;
